import OpenAI from 'openai';
import { RpaStep, ExecutionLog, RpaStepBase } from '@rpa-project/shared';
import { StepExecutionResult } from '../../base';

/**
 * LLM processing step executors for AIRunner
 */

export interface LlmProcessingExecutorContext {
  variables: Record<string, any>;
  onLog: (log: Omit<ExecutionLog, 'timestamp'>) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
  openai: OpenAI;
}

// Local type definition until shared package is updated
interface ProcessWithLLMStep extends RpaStepBase {
  type: 'processWithLLM';
  textInput: string; // Text to process or variable reference
  prompt: string; // User prompt for LLM processing
  variableName?: string; // Variable name to store AI response (default: 'var-ai-response')
  model?: string; // OpenAI model to use (default: 'gpt-4o-mini')
  temperature?: number; // Temperature for AI response (default: 0.3)
  maxTokens?: number; // Max tokens for response (default: 2000)
}

/**
 * Execute processWithLLM step - processes text with OpenAI LLM
 */
export async function executeProcessWithLLM(
  step: ProcessWithLLMStep,
  context: LlmProcessingExecutorContext
): Promise<StepExecutionResult> {
  const { variables, onLog, interpolateVariables, openai } = context;

  try {
    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OpenAI API key not configured');
    }

    // Interpolate inputs
    const interpolatedTextInput = interpolateVariables(step.textInput, variables);
    const interpolatedPrompt = interpolateVariables(step.prompt, variables);

    onLog({
      level: 'info',
      message: 'Sending text to OpenAI for processing...',
      stepId: step.id
    });

    // Send to OpenAI for processing
    const completion = await openai.chat.completions.create({
      model: step.model || 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: 'Du är en AI-assistent som hjälper till att extrahera och bearbeta information från dokument. Svara på svenska och var koncis och tydlig.'
        },
        {
          role: 'user',
          content: `Här är texten som ska bearbetas:\n\n${interpolatedTextInput}\n\nAnvändarens instruktion: ${interpolatedPrompt}\n\nBearbeta texten enligt instruktionen och ge ett tydligt svar.`
        }
      ],
      temperature: step.temperature || 0.3,
      max_tokens: step.maxTokens || 2000
    });

    const aiResponse = completion.choices[0]?.message?.content;
    if (!aiResponse) {
      throw new Error('No response from OpenAI');
    }

    // Store result in variable
    const variableName = step.variableName || 'var-ai-response';
    variables[variableName] = aiResponse;

    onLog({
      level: 'info',
      message: `LLM processing completed. Result stored in variable: ${variableName}`,
      stepId: step.id,
      data: { [variableName]: aiResponse.substring(0, 200) + (aiResponse.length > 200 ? '...' : '') }
    });

    return {
      success: true,
      variables: { [variableName]: aiResponse }
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    onLog({
      level: 'error',
      message: `Failed to process text with LLM: ${errorMessage}`,
      stepId: step.id
    });

    return {
      success: false,
      error: errorMessage
    };
  }
}

/**
 * PDF extraction and LLM processing with JSON value extraction
 */
export async function executeExtractPdfValues(
  step: any, // ExtractPdfValuesStep
  context: LlmProcessingExecutorContext
): Promise<StepExecutionResult> {
  const { variables, onLog, interpolateVariables, openai } = context;

  try {
    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OpenAI API key not configured');
    }

    // Interpolate base64Input to handle variable references
    const interpolatedBase64Input = interpolateVariables(step.base64Input, variables);
    const interpolatedPrompt = interpolateVariables(step.prompt, variables);

    onLog({
      level: 'info',
      message: 'Starting PDF text extraction...',
      stepId: step.id
    });

    // Extract base64 data (remove data URL prefix if present)
    let base64Data = interpolatedBase64Input;
    if (base64Data.includes(',')) {
      base64Data = base64Data.split(',')[1];
    }

    // Convert base64 to buffer
    const pdfBuffer = Buffer.from(base64Data, 'base64');

    onLog({
      level: 'info',
      message: `PDF buffer created, size: ${pdfBuffer.length} bytes`,
      stepId: step.id
    });

    // Extract text from PDF with fallback to pdfjs-dist if pdf-parse fails
    let extractedText: string;
    try {
      const pdfParse = require('pdf-parse');
      const pdfData = await pdfParse(pdfBuffer);
      extractedText = pdfData.text;

      onLog({
        level: 'info',
        message: 'PDF text extracted successfully using pdf-parse',
        stepId: step.id
      });
    } catch (pdfParseError) {
      onLog({
        level: 'warn',
        message: `pdf-parse failed (${pdfParseError instanceof Error ? pdfParseError.message : 'Unknown error'}), trying pdfjs-dist...`,
        stepId: step.id
      });

      // Fallback to pdfjs-dist
      try {
        const pdfjs = require('pdfjs-dist');
        const loadingTask = pdfjs.getDocument({ data: pdfBuffer });
        const pdf = await loadingTask.promise;

        let fullText = '';
        for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
          const page = await pdf.getPage(pageNum);
          const textContent = await page.getTextContent();
          const pageText = textContent.items
            .filter((item: any) => 'str' in item)
            .map((item: any) => item.str)
            .join(' ');
          fullText += pageText + '\n';
        }

        extractedText = fullText.trim();

        onLog({
          level: 'info',
          message: 'PDF text extracted successfully using pdfjs-dist fallback',
          stepId: step.id
        });
      } catch (pdfjsError) {
        throw new Error(`Both PDF parsers failed. pdf-parse: ${pdfParseError instanceof Error ? pdfParseError.message : 'Unknown error'}. pdfjs-dist: ${pdfjsError instanceof Error ? pdfjsError.message : 'Unknown error'}`);
      }
    }

    onLog({
      level: 'info',
      message: `Extracted ${extractedText.length} characters from PDF`,
      stepId: step.id
    });

    onLog({
      level: 'info',
      message: 'Sending text to OpenAI for processing...',
      stepId: step.id
    });

    // Create JSON system prompt for structured data extraction
    const jsonSystemPrompt = `Du är en AI-assistent som hjälper till att extrahera specifika värden från PDF-dokument.

VIKTIGT: Du MÅSTE alltid svara med ett giltigt JSON-objekt. Inget annat format accepteras.

Exempel på korrekt svar:
{
  "namn": "John Doe",
  "telefon": "08-123 456 78",
  "email": "<EMAIL>",
  "datum": "2024-01-15"
}

Om du inte kan hitta ett värde, använd null som värde. Svara alltid på svenska.`;

    // Send to OpenAI for processing
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: jsonSystemPrompt
        },
        {
          role: 'user',
          content: `Här är texten från ett PDF-dokument:\n\n${extractedText}\n\nAnvändarens instruktion: ${interpolatedPrompt}\n\nExtrahera de begärda värdena och svara med ett JSON-objekt.`
        }
      ],
      temperature: 0.1, // Lower temperature for more consistent JSON output
      max_tokens: 2000
    });

    const aiResponse = completion.choices[0]?.message?.content;
    if (!aiResponse) {
      throw new Error('No response from OpenAI');
    }

    onLog({
      level: 'info',
      message: 'AI response received, parsing JSON...',
      stepId: step.id
    });

    // Parse JSON response and create variables for each value
    let parsedJson: Record<string, any>;
    try {
      // Clean the response to extract JSON
      let jsonStr = aiResponse.trim();

      // Remove markdown code blocks if present
      if (jsonStr.startsWith('```json')) {
        jsonStr = jsonStr.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (jsonStr.startsWith('```')) {
        jsonStr = jsonStr.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      parsedJson = JSON.parse(jsonStr);

      if (typeof parsedJson !== 'object' || parsedJson === null || Array.isArray(parsedJson)) {
        throw new Error('Response is not a valid JSON object');
      }

    } catch (parseError) {
      const errorMessage = parseError instanceof Error ? parseError.message : 'Unknown JSON parsing error';
      onLog({
        level: 'error',
        message: `Failed to parse AI response as JSON: ${errorMessage}`,
        stepId: step.id,
        data: { rawResponse: aiResponse.substring(0, 500) }
      });

      throw new Error(`Invalid JSON response from AI: ${errorMessage}`);
    }

    // Create variables for each key-value pair in the JSON object
    const createdVariables: Record<string, any> = {};
    const variableNames: string[] = [];

    for (const [key, value] of Object.entries(parsedJson)) {
      // Create variable name with prefix to avoid conflicts
      const variableName = `var-${key}`;
      variables[variableName] = value;
      createdVariables[variableName] = value;
      variableNames.push(variableName);
    }

    onLog({
      level: 'info',
      message: `PDF values extraction completed. Created ${variableNames.length} variables: ${variableNames.join(', ')}`,
      stepId: step.id,
      data: createdVariables
    });

    return {
      success: true,
      variables: createdVariables
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    onLog({
      level: 'error',
      message: `Failed to extract PDF text: ${errorMessage}`,
      stepId: step.id
    });

    return {
      success: false,
      error: errorMessage
    };
  }
}
