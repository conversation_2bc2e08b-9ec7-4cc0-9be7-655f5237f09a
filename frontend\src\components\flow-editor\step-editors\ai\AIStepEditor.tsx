import { useState } from 'react'
import { validateStep, ExtractPdfValuesStep } from '@rpa-project/shared'
import { BaseStepEditorProps, StepEditorLayout, CommonStepFields } from '../base'
import { VariableField, TextAreaField } from '../base/FieldComponents'

type AIStep = ExtractPdfValuesStep

interface AIStepEditorProps extends BaseStepEditorProps {
  step: AIStep
}

export function AIStepEditor({ 
  step, 
  onSave, 
  onCancel, 
  compact = false, 
  steps = [], 
  currentStepIndex = 0 
}: AIStepEditorProps) {
  const [editedStep, setEditedStep] = useState<AIStep>({ ...step })
  const [errors, setErrors] = useState<string[]>([])

  const handleSave = () => {
    const validation = validateStep(editedStep)
    if (!validation.valid) {
      setErrors(validation.errors.map(e => e.message))
      return
    }

    setErrors([])
    onSave(editedStep)
  }

  const updateStep = (updates: Partial<AIStep>) => {
    setEditedStep(prev => ({ ...prev, ...updates } as AIStep))
  }

  const renderAIFields = () => {
    switch (editedStep.type) {
      case 'extractPdfValues':
        const pdfStep = editedStep as ExtractPdfValuesStep
        return (
          <>
            <VariableField
              label="Base64 PDF Data"
              value={pdfStep.base64Input || ''}
              onChange={(value) => updateStep({ base64Input: value })}
              placeholder="Ange base64 data eller välj variabel"
              steps={steps}
              currentStepIndex={currentStepIndex}
              compact={compact}
              fullWidth={true}
            />
            <TextAreaField
              label="AI Prompt för värdeextraktion"
              value={pdfStep.prompt || ''}
              onChange={(value) => updateStep({ prompt: value })}
              placeholder="Beskriv vilka värden du vill extrahera, t.ex. 'Extrahera namn, telefonnummer, email och datum från dokumentet'"
              minHeight="80px"
              compact={compact}
              fullWidth={true}
            />
            <div style={{
              padding: '10px',
              backgroundColor: '#f8f9fa',
              borderRadius: '4px',
              fontSize: '12px',
              color: '#666',
              marginTop: '8px'
            }}>
              <strong>Info:</strong> AI:n kommer att svara med JSON och skapa variabler automatiskt (var-namn, var-telefon, etc.)
            </div>
          </>
        )

      default:
        return (
          <div className="text-gray-500">
            No specific configuration available for this step type.
          </div>
        )
    }
  }

  const getTitle = () => {
    const stepNames = {
      extractPdfValues: 'Extract PDF Values'
    }

    const stepName = stepNames[editedStep.type as keyof typeof stepNames] || editedStep.type
    return compact ? `Konfigurera ${stepName}-steg` : `Edit ${stepName} Step`
  }

  return (
    <StepEditorLayout
      title={getTitle()}
      errors={errors}
      onSave={handleSave}
      onCancel={onCancel}
      compact={compact}
    >
      {renderAIFields()}
      <CommonStepFields 
        step={editedStep} 
        updateStep={updateStep} 
        compact={compact} 
      />
    </StepEditorLayout>
  )
}
