import { RpaStep, ConditionalClickStep, DownloadFileStep, ExtractPdfValuesStep } from './steps';
import { RpaFlow, FlowSchedule, FlowNode, FlowEdge, FlowData } from './flows';
import { generateId } from './base';

// Utility functions for creating steps
export function createEmptyFlow(name: string, customerId: string = ''): RpaFlow {
  return {
    id: generateId(),
    name,
    description: '',
    customerId,
    steps: [],
    createdAt: new Date(),
    updatedAt: new Date()
  }
}

export function createStepFromType(stepType: string): RpaStep {
  const baseStep = {
    id: generateId(),
    type: stepType as any,
    timeout: 30000
  }

  switch (stepType) {
    case 'navigate':
      return { ...baseStep, type: 'navigate', url: 'https://example.com', waitUntil: 'load' }
    case 'goBack':
      return { ...baseStep, type: 'goBack' }
    case 'goForward':
      return { ...baseStep, type: 'goForward' }
    case 'reload':
      return { ...baseStep, type: 'reload' }
    case 'click':
      return { ...baseStep, type: 'click', selector: 'button', button: 'left' }
    case 'fill':
      return { ...baseStep, type: 'fill', selector: 'input', value: 'placeholder text' }
    case 'type':
      return { ...baseStep, type: 'type', selector: 'input', text: 'text to type', delay: 0 }
    case 'selectOption':
      return { ...baseStep, type: 'selectOption', selector: 'select', value: 'option-value' }
    case 'check':
      return { ...baseStep, type: 'check', selector: 'input[type="checkbox"]' }
    case 'uncheck':
      return { ...baseStep, type: 'uncheck', selector: 'input[type="checkbox"]' }
    case 'waitForSelector':
      return { ...baseStep, type: 'waitForSelector', selector: 'element', state: 'visible' }
    case 'waitForTimeout':
      return { ...baseStep, type: 'waitForTimeout', duration: 1000 }
    case 'extractText':
      return { ...baseStep, type: 'extractText', selector: 'element', variableName: 'extractedText' }
    case 'takeScreenshot':
      return { ...baseStep, type: 'takeScreenshot', fullPage: false }
    case 'conditionalClick':
      return { ...baseStep, type: 'conditionalClick', selector: 'button', condition: 'exists', clickIfTrue: true, button: 'left' }
    case 'fillPassword':
      return { ...baseStep, type: 'fillPassword', selector: 'input[type="password"]', credentialId: '' }
    case 'fill2FA':
      return { ...baseStep, type: 'fill2FA', selector: 'input', credentialId: '' }
    case 'downloadFile':
      return { ...baseStep, type: 'downloadFile', triggerSelector: 'a[href*=".pdf"]', filename: '', variableName: '', saveToFile: false, forceDownload: false }
    case 'extractPdfValues':
      return { ...baseStep, type: 'extractPdfValues', base64Input: '', prompt: 'Extrahera namn, telefonnummer och email från dokumentet' }
    default:
      throw new Error(`Unknown step type: ${stepType}`)
  }
}

export function getStepLabel(step: RpaStep): string {
  switch (step.type) {
    case 'navigate':
      return step.url || 'Navigate to URL'
    case 'goBack':
      return 'Go back'
    case 'goForward':
      return 'Go forward'
    case 'reload':
      return 'Reload page'
    case 'click':
      return step.selector || 'Click element'
    case 'fill':
      return `Fill: ${step.selector || 'element'}`
    case 'type':
      return `Type: ${step.text || 'text'}`
    case 'selectOption':
      return `Select: ${step.value || step.label || (step.index !== undefined ? `index ${step.index}` : 'option')}`
    case 'check':
      return `Check: ${step.selector || 'checkbox'}`
    case 'uncheck':
      return `Uncheck: ${step.selector || 'checkbox'}`
    case 'waitForSelector':
      return `Wait for: ${step.selector || 'element'}`
    case 'waitForTimeout':
      return `Wait ${step.duration || 1000}ms`
    case 'extractText':
      return `Extract: ${step.variableName || 'text'}`
    case 'takeScreenshot':
      return 'Take screenshot'
    case 'conditionalClick':
      const condStep = step as ConditionalClickStep
      const action = condStep.clickIfTrue ? 'Click if' : 'Click if not'
      return `${action} ${condStep.condition}: ${condStep.selector || 'element'}`
    case 'fillPassword':
      return `Fill password: ${step.selector || 'field'}`
    case 'fill2FA':
      return `Fill 2FA: ${step.selector || 'field'}`
    case 'downloadFile':
      const downloadStep = step as DownloadFileStep
      return downloadStep.triggerSelector
        ? `Download via: ${downloadStep.triggerSelector}`
        : 'Download file'
    case 'extractPdfValues':
      const extractStep = step as ExtractPdfValuesStep
      return `Extract PDF values: ${extractStep.prompt.substring(0, 50)}...`
    default:
      return step.type
  }
}

// Utility functions for schedules
export function createEmptySchedule(flowId: string, name: string): FlowSchedule {
  return {
    id: generateId(),
    flowId,
    name,
    description: '',
    cronExpression: '0 9 * * 1-5', // Default: 9 AM on weekdays
    timezone: 'UTC',
    enabled: true,
    variables: {},
    createdAt: new Date(),
    updatedAt: new Date()
  }
}

// React Flow conversion functions
export function convertFlowToReactFlow(flow: RpaFlow): FlowData {
  const nodes: FlowNode[] = flow.steps.map((step, index) => {
    // Create better positioning for AI-generated flows
    // Use a slight horizontal offset to prevent overlapping
    const baseX = 100
    const baseY = 50
    const verticalSpacing = 120
    const horizontalOffset = (index % 2) * 30 // Small alternating offset

    return {
      id: step.id,
      type: 'rpaStep',
      position: {
        x: baseX + horizontalOffset,
        y: baseY + (index * verticalSpacing)
      },
      data: {
        step,
        label: getStepLabel(step)
      }
    }
  })

  const edges: FlowEdge[] = []
  for (let i = 0; i < flow.steps.length - 1; i++) {
    edges.push({
      id: `${flow.steps[i].id}-${flow.steps[i + 1].id}`,
      source: flow.steps[i].id,
      target: flow.steps[i + 1].id,
      type: 'smoothstep'
    })
  }

  return { nodes, edges }
}

export function convertReactFlowToFlow(flowData: FlowData, flowId: string, flowName: string, customerId: string = ''): RpaFlow {
  // Build a map of connections
  const connections = new Map<string, string>()
  flowData.edges.forEach(edge => {
    connections.set(edge.source, edge.target)
  })

  // Find the starting node (no incoming edges)
  const hasIncoming = new Set(flowData.edges.map(e => e.target))
  const startNodes = flowData.nodes.filter(node => !hasIncoming.has(node.id))

  if (startNodes.length === 0 && flowData.nodes.length > 0) {
    // If no clear start, use first node
    const steps = flowData.nodes.map(node => node.data.step)
    return {
      id: flowId,
      name: flowName,
      description: '',
      customerId,
      steps,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }

  // Build ordered steps by following connections
  const steps: RpaStep[] = []
  const visited = new Set<string>()

  function addStepsFromNode(nodeId: string) {
    if (visited.has(nodeId)) return
    visited.add(nodeId)

    const node = flowData.nodes.find(n => n.id === nodeId)
    if (node) {
      steps.push(node.data.step)
      const nextNodeId = connections.get(nodeId)
      if (nextNodeId) {
        addStepsFromNode(nextNodeId)
      }
    }
  }

  if (startNodes.length > 0) {
    addStepsFromNode(startNodes[0].id)
  }

  return {
    id: flowId,
    name: flowName,
    description: '',
    customerId,
    steps,
    createdAt: new Date(),
    updatedAt: new Date()
  }
}
