// Export all step types and create RpaStep union
export * from './base';
export * from './navigation';
export * from './interaction';
export * from './waiting';
export * from './extraction';
export * from './credentials';
export * from './files';
export * from './conditional';
export * from './ai';

// Import all step types for union
import type { NavigateStep, GoBackStep, GoForwardStep, ReloadStep } from './navigation';
import type { ClickStep, FillStep, TypeStep, SelectOptionStep, CheckStep, UncheckStep } from './interaction';
import type { WaitForSelectorStep, WaitForTimeoutStep, WaitForUrlStep } from './waiting';
import type { ExtractTextStep, ExtractAttributeStep, TakeScreenshotStep } from './extraction';
import type { FillPasswordStep, Fill2FAStep } from './credentials';
import type { DownloadFileStep } from './files';
import type { IfElementExistsStep, ConditionalClickStep } from './conditional';
import type { ExtractPdfValuesStep } from './ai';

// Union type for all step types
export type RpaStep =
  | NavigateStep
  | GoBackStep
  | GoForwardStep
  | ReloadStep
  | ClickStep
  | FillStep
  | TypeStep
  | SelectOptionStep
  | CheckStep
  | UncheckStep
  | WaitForSelectorStep
  | WaitForTimeoutStep
  | WaitForUrlStep
  | ExtractTextStep
  | ExtractAttributeStep
  | TakeScreenshotStep
  | IfElementExistsStep
  | ConditionalClickStep
  | FillPasswordStep
  | Fill2FAStep
  | DownloadFileStep
  | ExtractPdfValuesStep;
